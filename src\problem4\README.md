# Problem 4: Three Ways to Sum to n

This directory contains three unique implementations of a function that calculates the sum of integers from 1 to n (inclusive) in TypeScript.

## Problem Statement

**Input**: `n` - any integer  
**Output**: `return` - summation to `n`, i.e. `sum_to_n(5) === 1 + 2 + 3 + 4 + 5 === 15`

*Assuming this input will always produce a result lesser than `Number.MAX_SAFE_INTEGER`.*

## Implementations

### 1. Mathematical Formula Approach (`sum_to_n_a`)

Uses the arithmetic series formula: `n * (n + 1) / 2`

```typescript
function sum_to_n_a(n: number): number {
    if (n <= 0) return 0;
    return (n * (n + 1)) / 2;
}
```

**Complexity:**
- Time: O(1) - Constant time
- Space: O(1) - Constant space

**Efficiency:** ⭐⭐⭐⭐⭐ (Best)  
Most efficient approach as it performs the calculation in a single mathematical operation regardless of input size.

### 2. Iterative Loop Approach (`sum_to_n_b`)

Uses a for loop to iterate through all numbers from 1 to n.

```typescript
function sum_to_n_b(n: number): number {
    if (n <= 0) return 0;
    let sum = 0;
    for (let i = 1; i <= n; i++) {
        sum += i;
    }
    return sum;
}
```

**Complexity:**
- Time: O(n) - Linear time
- Space: O(1) - Constant space

**Efficiency:** ⭐⭐⭐ (Good)  
Moderate efficiency with linear time complexity but constant space usage. Intuitive and easy to understand.

### 3. Recursive Approach (`sum_to_n_c`)

Uses recursion where `sum(n) = n + sum(n-1)` with base case `sum(0) = 0`.

```typescript
function sum_to_n_c(n: number): number {
    if (n <= 0) return 0;
    return n + sum_to_n_c(n - 1);
}
```

**Complexity:**
- Time: O(n) - Linear time
- Space: O(n) - Linear space (call stack)

**Efficiency:** ⭐⭐ (Fair)  
Least efficient due to function call overhead and linear space usage. Risk of stack overflow for large n.

## Files

- `sum_to_n.ts` - Main implementation file with all three functions
- `test_sum_to_n.ts` - Comprehensive test suite and performance benchmarks
- `README.md` - This documentation file

## Usage

```typescript
import { sum_to_n_a, sum_to_n_b, sum_to_n_c } from './sum_to_n';

// All three functions produce the same result
console.log(sum_to_n_a(5));  // 15
console.log(sum_to_n_b(5));  // 15
console.log(sum_to_n_c(5));  // 15
```

## Running Tests

To run the comprehensive test suite:

```bash
# If using Node.js with TypeScript
npx ts-node test_sum_to_n.ts

# Or compile and run
tsc test_sum_to_n.ts && node test_sum_to_n.js
```

## Performance Comparison

| Implementation | Time Complexity | Space Complexity | Best Use Case |
|----------------|-----------------|------------------|---------------|
| Formula (A)    | O(1)           | O(1)             | Production code, performance-critical |
| Iterative (B)  | O(n)           | O(1)             | Educational, step-by-step understanding |
| Recursive (C)  | O(n)           | O(n)             | Academic, demonstrating recursion |

## Recommendations

- **Use Implementation A (Formula)** for production code due to optimal O(1) performance
- **Use Implementation B (Iterative)** when you need to understand the step-by-step process or for educational purposes
- **Use Implementation C (Recursive)** for academic demonstrations or when recursion fits the problem domain better

## Mathematical Background

The sum of the first n positive integers is given by the arithmetic series formula:

```
Sum = 1 + 2 + 3 + ... + n = n(n+1)/2
```

This formula was famously discovered by Carl Friedrich Gauss as a young student when asked to sum the numbers from 1 to 100.

## Edge Cases Handled

- `n = 0`: Returns 0
- `n < 0`: Returns 0 (negative inputs treated as 0)
- Large values of `n`: All implementations handle values up to `Number.MAX_SAFE_INTEGER`
