/**
 * Problem 4: Three Ways to Sum to n
 * 
 * This file contains three unique implementations of a function that calculates
 * the sum of integers from 1 to n (inclusive).
 */

/**
 * Implementation 1: Mathematical Formula Approach
 * 
 * Uses the mathematical formula: sum = n * (n + 1) / 2
 * This is based on the arithmetic series formula.
 * 
 * Time Complexity: O(1) - Constant time
 * Space Complexity: O(1) - Constant space
 * 
 * Efficiency: Most efficient approach as it performs the calculation
 * in a single mathematical operation regardless of the input size.
 * 
 * @param n - The upper bound integer (inclusive)
 * @returns The sum of integers from 1 to n
 */
function sum_to_n_a(n: number): number {
    // Handle edge cases
    if (n <= 0) return 0;
    
    // Apply the mathematical formula: n * (n + 1) / 2
    return (n * (n + 1)) / 2;
}

/**
 * Implementation 2: Iterative Loop Approach
 * 
 * Uses a for loop to iterate through all numbers from 1 to n
 * and accumulates the sum.
 * 
 * Time Complexity: O(n) - Linear time
 * Space Complexity: O(1) - Constant space
 * 
 * Efficiency: Moderate efficiency. The time complexity grows linearly
 * with the input size, but it uses constant space. This approach is
 * intuitive and easy to understand.
 * 
 * @param n - The upper bound integer (inclusive)
 * @returns The sum of integers from 1 to n
 */
function sum_to_n_b(n: number): number {
    // Handle edge cases
    if (n <= 0) return 0;
    
    let sum = 0;
    
    // Iterate from 1 to n and accumulate the sum
    for (let i = 1; i <= n; i++) {
        sum += i;
    }
    
    return sum;
}

/**
 * Implementation 3: Recursive Approach
 * 
 * Uses recursion where sum(n) = n + sum(n-1) with base case sum(0) = 0.
 * 
 * Time Complexity: O(n) - Linear time
 * Space Complexity: O(n) - Linear space (due to call stack)
 * 
 * Efficiency: Least efficient approach due to function call overhead
 * and linear space usage from the call stack. However, it demonstrates
 * the mathematical relationship clearly and is elegant in its simplicity.
 * Note: For very large n, this could cause stack overflow.
 * 
 * @param n - The upper bound integer (inclusive)
 * @returns The sum of integers from 1 to n
 */
function sum_to_n_c(n: number): number {
    // Base case: sum of numbers up to 0 is 0
    if (n <= 0) return 0;
    
    // Recursive case: n + sum of numbers up to (n-1)
    return n + sum_to_n_c(n - 1);
}

// Export all three implementations
export { sum_to_n_a, sum_to_n_b, sum_to_n_c };

/**
 * Complexity Comparison Summary:
 * 
 * | Implementation | Time Complexity | Space Complexity | Efficiency Rank |
 * |----------------|-----------------|------------------|-----------------|
 * | Formula (A)    | O(1)           | O(1)             | 1st (Best)      |
 * | Iterative (B)  | O(n)           | O(1)             | 2nd             |
 * | Recursive (C)  | O(n)           | O(n)             | 3rd (Worst)     |
 * 
 * Recommendations:
 * - Use Implementation A (formula) for production code due to optimal performance
 * - Use Implementation B (iterative) when you need to understand the step-by-step process
 * - Use Implementation C (recursive) for educational purposes or when the recursive
 *   nature fits the problem domain better
 */
