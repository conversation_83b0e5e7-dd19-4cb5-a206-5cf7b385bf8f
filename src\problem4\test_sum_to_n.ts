/**
 * Test file for the three sum_to_n implementations
 * 
 * This file contains comprehensive test cases and performance benchmarks
 * for all three implementations of the sum_to_n function.
 */

import { sum_to_n_a, sum_to_n_b, sum_to_n_c } from './sum_to_n';

/**
 * Test cases with expected results
 */
const testCases = [
    { input: 0, expected: 0, description: "Sum to 0" },
    { input: 1, expected: 1, description: "Sum to 1" },
    { input: 5, expected: 15, description: "Sum to 5 (1+2+3+4+5)" },
    { input: 10, expected: 55, description: "Sum to 10" },
    { input: 100, expected: 5050, description: "Sum to 100" },
    { input: -5, expected: 0, description: "Negative input" },
    { input: 1000, expected: 500500, description: "Sum to 1000" }
];

/**
 * Function to run tests for a specific implementation
 */
function runTests(func: (n: number) => number, funcName: string): void {
    console.log(`\n=== Testing ${funcName} ===`);
    
    let passedTests = 0;
    
    testCases.forEach((testCase, index) => {
        const result = func(testCase.input);
        const passed = result === testCase.expected;
        
        console.log(`Test ${index + 1}: ${testCase.description}`);
        console.log(`  Input: ${testCase.input}`);
        console.log(`  Expected: ${testCase.expected}`);
        console.log(`  Got: ${result}`);
        console.log(`  Status: ${passed ? '✅ PASS' : '❌ FAIL'}`);
        
        if (passed) passedTests++;
    });
    
    console.log(`\nResults: ${passedTests}/${testCases.length} tests passed`);
}

/**
 * Performance benchmark function
 */
function benchmark(func: (n: number) => number, funcName: string, n: number, iterations: number = 100000): void {
    const startTime = performance.now();
    
    for (let i = 0; i < iterations; i++) {
        func(n);
    }
    
    const endTime = performance.now();
    const totalTime = endTime - startTime;
    const avgTime = totalTime / iterations;
    
    console.log(`${funcName}: ${totalTime.toFixed(2)}ms total, ${avgTime.toFixed(6)}ms average per call`);
}

/**
 * Run all tests
 */
function runAllTests(): void {
    console.log("🧪 Running comprehensive tests for all sum_to_n implementations\n");
    
    // Test all implementations
    runTests(sum_to_n_a, "sum_to_n_a (Mathematical Formula)");
    runTests(sum_to_n_b, "sum_to_n_b (Iterative Loop)");
    runTests(sum_to_n_c, "sum_to_n_c (Recursive)");
    
    console.log("\n" + "=".repeat(60));
    console.log("📊 PERFORMANCE BENCHMARK");
    console.log("=".repeat(60));
    
    // Performance comparison for different input sizes
    const benchmarkSizes = [10, 100, 1000];
    
    benchmarkSizes.forEach(size => {
        console.log(`\nBenchmark for n = ${size} (100,000 iterations each):`);
        benchmark(sum_to_n_a, "Formula     ", size);
        benchmark(sum_to_n_b, "Iterative   ", size);
        
        // For recursive, use fewer iterations for larger n to avoid stack overflow
        const recursiveIterations = size > 100 ? 1000 : 100000;
        if (size <= 1000) { // Avoid stack overflow for very large numbers
            benchmark(sum_to_n_c, "Recursive   ", size, recursiveIterations);
            if (recursiveIterations < 100000) {
                console.log("  (Note: Recursive used fewer iterations to prevent stack overflow)");
            }
        } else {
            console.log("Recursive   : Skipped (risk of stack overflow)");
        }
    });
}

/**
 * Demonstrate usage examples
 */
function demonstrateUsage(): void {
    console.log("\n" + "=".repeat(60));
    console.log("💡 USAGE EXAMPLES");
    console.log("=".repeat(60));
    
    const examples = [5, 10, 50];
    
    examples.forEach(n => {
        console.log(`\nCalculating sum from 1 to ${n}:`);
        console.log(`  Formula approach:   ${sum_to_n_a(n)}`);
        console.log(`  Iterative approach: ${sum_to_n_b(n)}`);
        console.log(`  Recursive approach: ${sum_to_n_c(n)}`);
        
        // Show the manual calculation for smaller numbers
        if (n <= 10) {
            const sequence = Array.from({length: n}, (_, i) => i + 1).join(' + ');
            console.log(`  Manual calculation: ${sequence} = ${sum_to_n_a(n)}`);
        }
    });
}

// Run the tests if this file is executed directly
if (require.main === module) {
    runAllTests();
    demonstrateUsage();
    
    console.log("\n" + "=".repeat(60));
    console.log("✨ All tests completed!");
    console.log("=".repeat(60));
}

// Export test functions for external use
export { runAllTests, runTests, benchmark, demonstrateUsage };
